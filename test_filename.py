#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import os
from datetime import datetime

def safe_clean_filename(filename):
    """清理文件名中的非法字符"""
    return re.sub(r'[\\/*?:"<>|]', "_", filename)

# 测试文件名清理
sector_name = '电源设备'
safe_name = safe_clean_filename(sector_name)
filename = f"行业_stocks_{safe_name}"

print(f"原始名称: {sector_name}")
print(f"清理后名称: {safe_name}")
print(f"完整文件名: {filename}")

# 测试完整路径
BASE_DATA_PATH = r"D:\dev\mootdx\adata\Gemini\fund_data"
date_str = datetime.now().strftime('%Y-%m-%d')
folder = os.path.join(BASE_DATA_PATH, date_str)
timestamp = "2025-07-29_11-27-44"
file_path = os.path.join(folder, f'{filename}_{timestamp}.csv')

print(f"完整文件路径: {file_path}")
print(f"文件是否存在: {os.path.exists(file_path)}")

# 列出目录中的所有文件，查找可能的匹配
if os.path.exists(folder):
    files = os.listdir(folder)
    print(f"\n目录中的文件数量: {len(files)}")
    
    # 查找包含"电源"的文件
    matching_files = [f for f in files if '电源' in f]
    if matching_files:
        print(f"包含'电源'的文件: {matching_files}")
    else:
        print("未找到包含'电源'的文件")
    
    # 查找包含"行业_stocks"的文件
    stock_files = [f for f in files if '行业_stocks' in f]
    if stock_files:
        print(f"包含'行业_stocks'的文件: {stock_files}")
    else:
        print("未找到包含'行业_stocks'的文件")
        
    # 查找11-27时间段的文件
    time_files = [f for f in files if '11-27' in f]
    if time_files:
        print(f"11-27时间段的文件: {time_files}")
