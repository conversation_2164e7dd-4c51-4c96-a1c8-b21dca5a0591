#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资金流向查询工具
读取fund_flow.db数据库，查询指定日期的个股资金流入数据
"""

import sqlite3
import os

# 配置参数
DB_PATH = r"F:\his"  # 数据库文件夹路径
DB_NAME = "fund_flow.db"  # 数据库文件名
QUERY_DATE = "2025-07-25"  # 查询日期，格式：YYYY-MM-DD
QUERY_TYPE = "创新高"  # 查询类型：排行榜 或 创新高

def get_db_connection():
    """获取数据库连接"""
    db_file = os.path.join(DB_PATH, DB_NAME)
    
    if not os.path.exists(db_file):
        raise FileNotFoundError(f"数据库文件不存在: {db_file}")
    
    try:
        conn = sqlite3.connect(db_file)
        return conn
    except sqlite3.Error as e:
        raise Exception(f"连接数据库失败: {e}")

def query_ranking():
    """排行榜查询：查询指定日期的资金流入排行榜"""
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 查询数据，关联获取股票名称
        query = """
        SELECT 
            d.stock_code,
            r.stock_name,
            d.main_net_inflow,
            d.super_large_net_inflow,
            d.large_net_inflow,
            d.close_price,
            d.change_pct
        FROM daily_fund_flow d
        LEFT JOIN daily_rankings r ON d.stock_code = r.stock_code AND d.date = r.date
        WHERE d.date = ?
        ORDER BY d.main_net_inflow DESC
        """
        
        cursor.execute(query, (QUERY_DATE,))
        results = cursor.fetchall()
        
        if not results:
            print(f"未找到日期 {QUERY_DATE} 的资金流入数据")
            return
        
        print(f"=== 日期 {QUERY_DATE} 资金流入前20名 ===")
        print(f"{'代码':<10} {'股票名称':<15} {'主力净流入':<15} {'超大单':<15} {'大单':<15} {'收盘价':<10} {'涨跌幅':<10}")
        print("-" * 100)
        
        total_main = 0
        for i, row in enumerate(results[:20], 1):
            stock_code, stock_name, main_inflow, super_large, large, close_price, change_pct = row
            total_main += main_inflow
            
            print(f"{stock_code:<10} {stock_name:<15} {main_inflow:>13,.0f} {super_large:>13,.0f} {large:>13,.0f} {close_price:>8.2f} {change_pct:>8.2f}%")
        
        print(f"\n=== 统计信息 ===")
        print(f"前20名主力资金净流入总计: {total_main:,.2f}")
        print(f"总数据条数: {len(results)}")
        
        # 保存到文本文件
        output_file = os.path.join(DB_PATH, f"fund_flow_ranking_{QUERY_DATE.replace('-', '')}.txt")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"=== 日期 {QUERY_DATE} 资金流入排行榜 ===\n\n")
            f.write(f"{'代码':<10} {'股票名称':<15} {'主力净流入':<15} {'超大单':<15} {'大单':<15} {'收盘价':<10} {'涨跌幅':<10}\n")
            f.write("-" * 100 + "\n")
            
            for row in results:
                stock_code, stock_name, main_inflow, super_large, large, close_price, change_pct = row
                f.write(f"{stock_code:<10} {stock_name:<15} {main_inflow:>13,.0f} {super_large:>13,.0f} {large:>13,.0f} {close_price:>8.2f} {change_pct:>8.2f}%\n")
            
            f.write(f"\n=== 统计信息 ===\n")
            f.write(f"主力资金净流入总计: {sum(r[2] for r in results):,.2f}\n")
            f.write(f"数据条数: {len(results)}\n")
        
        print(f"完整数据已保存到: {output_file}")
        
    except sqlite3.Error as e:
        print(f"数据库查询错误: {e}")
    finally:
        if conn:
            conn.close()

def query_new_high():
    """创新高查询：查询主力净流入创新高的股票"""
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 查询主力净流入创新高的股票
        # 使用 historical_max_rankings 表来查找历史最高值
        query = """
        SELECT 
            h.stock_code,
            h.stock_name,
            h.current_value,
            h.historical_max_value,
            h.historical_max_date,
            d.close_price,
            d.change_pct
        FROM historical_max_rankings h
        JOIN daily_fund_flow d ON h.stock_code = d.stock_code AND h.date = d.date
        WHERE h.date = ? 
        AND h.metric = 'main_net_inflow'
        AND h.is_historical_max = 1
        ORDER BY h.current_value DESC
        """
        
        cursor.execute(query, (QUERY_DATE,))
        results = cursor.fetchall()
        
        if not results:
            print(f"未找到日期 {QUERY_DATE} 主力净流入创新高的股票")
            return
        
        print(f"=== 日期 {QUERY_DATE} 主力净流入创新高股票 ===")
        print(f"{'代码':<10} {'股票名称':<15} {'当前值':<15} {'历史最高':<15} {'历史最高日期':<15} {'收盘价':<10} {'涨跌幅':<10}")
        print("-" * 95)
        
        total_current = 0
        for i, row in enumerate(results, 1):
            stock_code, stock_name, current_value, historical_max, historical_max_date, close_price, change_pct = row
            total_current += current_value
            
            print(f"{stock_code:<10} {stock_name:<15} {current_value:>13,.0f} {historical_max:>13,.0f} {historical_max_date:<15} {close_price:>8.2f} {change_pct:>8.2f}%")
        
        print(f"\n=== 统计信息 ===")
        print(f"创新高股票数量: {len(results)}")
        print(f"当前主力净流入总计: {total_current:,.2f}")
        
        # 保存到文本文件
        output_file = os.path.join(DB_PATH, f"fund_flow_new_high_{QUERY_DATE.replace('-', '')}.txt")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"=== 日期 {QUERY_DATE} 主力净流入创新高股票 ===\n\n")
            f.write(f"{'代码':<10} {'股票名称':<15} {'当前值':<15} {'历史最高':<15} {'历史最高日期':<15} {'收盘价':<10} {'涨跌幅':<10}\n")
            f.write("-" * 95 + "\n")
            
            for row in results:
                stock_code, stock_name, current_value, historical_max, historical_max_date, close_price, change_pct = row
                f.write(f"{stock_code:<10} {stock_name:<15} {current_value:>13,.0f} {historical_max:>13,.0f} {historical_max_date:<15} {close_price:>8.2f} {change_pct:>8.2f}%\n")
            
            f.write(f"\n=== 统计信息 ===\n")
            f.write(f"创新高股票数量: {len(results)}\n")
            f.write(f"当前主力净流入总计: {total_current:,.2f}\n")
        
        print(f"完整数据已保存到: {output_file}")
        
    except sqlite3.Error as e:
        print(f"数据库查询错误: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("=== 资金流向查询工具 ===")
    print(f"数据库路径: {DB_PATH}")
    print(f"数据库文件: {DB_NAME}")
    print(f"查询日期: {QUERY_DATE}")
    print(f"查询类型: {QUERY_TYPE}")
    
    try:
        if QUERY_TYPE == "排行榜":
            query_ranking()
        elif QUERY_TYPE == "创新高":
            query_new_high()
        else:
            print(f"错误：不支持的查询类型 '{QUERY_TYPE}'，请使用 '排行榜' 或 '创新高'")
    except Exception as e:
        print(f"执行错误: {e}")