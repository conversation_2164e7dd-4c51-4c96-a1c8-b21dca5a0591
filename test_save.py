#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import os
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_date_folder():
    """获取按日期命名的文件夹路径"""
    BASE_DATA_PATH = r"D:\dev\mootdx\adata\Gemini\fund_data"
    date_str = datetime.now().strftime('%Y-%m-%d')
    folder = os.path.join(BASE_DATA_PATH, date_str)
    os.makedirs(folder, exist_ok=True)
    return folder

def safe_clean_filename(filename):
    """清理文件名中的非法字符"""
    import re
    return re.sub(r'[\\/*?:"<>|]', "_", filename)

def save_data(df, filename):
    """保存数据到CSV文件"""
    if df.empty:
        logging.warning(f"数据为空，跳过保存文件: {filename}")
        return
    
    try:
        date_folder = get_date_folder()
        timestamp = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
        file_path = os.path.join(date_folder, f'{filename}_{timestamp}.csv')
        
        print(f"准备保存到: {file_path}")
        logging.info(f"准备保存到: {file_path}")
        
        # 检查目录权限
        if not os.access(date_folder, os.W_OK):
            logging.error(f"没有写入权限: {date_folder}")
            return
            
        df.to_csv(file_path, index=False, encoding='utf-8-sig')
        
        # 验证文件是否真的被创建
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            logging.info(f"✅ 成功保存数据到: {file_path}, 文件大小: {file_size} 字节")
            print(f"✅ 保存: {file_path}, 大小: {file_size} 字节")
        else:
            logging.error(f"❌ 文件保存失败，文件不存在: {file_path}")
            print(f"❌ 文件保存失败，文件不存在: {file_path}")
        
    except Exception as e:
        logging.error(f"保存文件失败: {filename}, 错误: {e}")
        print(f"❌ 保存文件失败: {filename}, 错误: {e}")
        import traceback
        logging.error(f"详细错误: {traceback.format_exc()}")

# 创建测试数据
test_data = {
    '代码': ['000001', '000002', '000003'],
    '名称': ['平安银行', '万科A', '国农科技'],
    '今日主力净流入-净额': [1000000, -500000, 200000],
    '今日涨跌幅': [2.5, -1.2, 0.8]
}

df = pd.DataFrame(test_data)
print(f"测试数据: {len(df)} 行")
print(df)

# 测试保存
sector_name = '电源设备'
safe_name = safe_clean_filename(sector_name)
filename = f"行业_stocks_{safe_name}"

print(f"\n开始测试保存...")
save_data(df, filename)
